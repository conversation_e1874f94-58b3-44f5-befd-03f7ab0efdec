#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版Word文档到Markdown转换器
支持：
1. 表格转换
2. 图片提取和引用
3. 段落格式保持（标题、列表、普通段落）
4. 文档结构保持
"""

from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
import os
import shutil
import zipfile
from docx.oxml import parse_xml
from docx.oxml.ns import nsmap
import re
from PIL import Image
import base64
from io import BytesIO
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("警告: pytesseract未安装，无法进行OCR识别。如需OCR功能，请安装: pip install pytesseract")
import xml.etree.ElementTree as ET


class EnhancedDocxToMarkdown:
    def __init__(self, docx_path, output_dir="output"):
        self.docx_path = docx_path
        self.output_dir = output_dir
        self.image_dir = os.path.join(output_dir, "images")
        self.doc = Document(docx_path)
        self.image_counter = 0
        self.image_map = {}

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)

    def extract_images(self):
        """提取文档中的所有图片并进行OCR识别"""
        image_files = []

        try:
            with zipfile.ZipFile(self.docx_path, 'r') as docx_zip:
                # 查找所有图片文件
                for file_info in docx_zip.filelist:
                    if file_info.filename.startswith('word/media/'):
                        # 提取图片
                        image_data = docx_zip.read(file_info.filename)

                        # 获取文件扩展名
                        _, ext = os.path.splitext(file_info.filename)
                        if not ext:
                            ext = '.png'  # 默认扩展名

                        # 生成新的文件名
                        self.image_counter += 1
                        new_filename = f"image_{self.image_counter:03d}{ext}"
                        image_path = os.path.join(self.image_dir, new_filename)

                        # 保存图片
                        with open(image_path, 'wb') as img_file:
                            img_file.write(image_data)

                        # OCR识别图片内容
                        print(f"正在进行OCR识别: {new_filename}")
                        ocr_text = self.extract_text_from_image(image_path)

                        # 记录映射关系（包含OCR结果）
                        original_name = os.path.basename(file_info.filename)
                        self.image_map[original_name] = {
                            'filename': new_filename,
                            'ocr_text': ocr_text
                        }
                        image_files.append(new_filename)

                        print(f"提取图片: {original_name} -> {new_filename}")
                        if ocr_text:
                            print(f"  ✅ OCR识别成功: {ocr_text[:50]}..." if len(ocr_text) > 50 else f"  ✅ OCR识别成功: {ocr_text}")
                        else:
                            print(f"  ⚠️  OCR识别为空或失败")

        except Exception as e:
            print(f"提取图片时出错: {e}")

        return image_files

    def extract_text_from_image(self, image_path):
        """使用OCR从图片中提取文本"""
        if not OCR_AVAILABLE:
            return ""

        try:
            # 打开图片
            image = Image.open(image_path)

            # 检查图片是否有效
            if image.size[0] < 10 or image.size[1] < 10:
                print(f"图片 {image_path} 尺寸太小，跳过OCR识别")
                return ""

            # 图片预处理：转为灰度图像并增强对比度
            image = image.convert('RGB').convert('L')  # 先转为RGB再转为灰度图

            # 增强对比度
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)  # 增强对比度

            # 尝试多种OCR模式
            ocr_configs = [
                r'--oem 3 --psm 6 -l chi_sim+eng',  # 单一文本块
                r'--oem 3 --psm 4 -l chi_sim+eng',  # 单列可变大小文本
                r'--oem 3 --psm 8 -l chi_sim+eng',  # 单词
                r'--oem 3 --psm 13 -l chi_sim+eng'  # 原始线，适合表格
            ]

            best_text = ""
            best_score = 0

            for config in ocr_configs:
                try:
                    text = pytesseract.image_to_string(image, config=config)

                    # 评估识别质量（简单评分）
                    score = self._evaluate_ocr_quality(text)

                    if score > best_score:
                        best_score = score
                        best_text = text

                except:
                    continue

            # 清理识别结果
            cleaned_text = self._clean_ocr_text(best_text)

            return cleaned_text

        except Exception as e:
            print(f"OCR识别图片 {image_path} 时出错: {e}")
            return ""

    def _evaluate_ocr_quality(self, text):
        """评估OCR识别质量"""
        if not text or len(text.strip()) < 5:
            return 0

        score = 0

        # 有意义的单词加分
        meaningful_words = ['instanceid', 'componenttype', 'status', 'runsonhost',
                           'apacheproxy', 'active', 'standby', 'success', 'fault',
                           'template', 'action', 'result', 'stop', 'start']

        for word in meaningful_words:
            if word.lower() in text.lower():
                score += 10

        # 表格结构加分
        if '|' in text:
            score += 5

        # 数字和IP地址加分
        if re.search(r'\d+\.\d+\.\d+\.\d+', text):  # IP地址
            score += 8

        if re.search(r'[A-F0-9]{8}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{4}-[A-F0-9]{12}', text):  # UUID
            score += 10

        # 减去无意义字符的分数
        nonsense_patterns = [r'nnn+', r'mmm+', r'xxx+', r'[a-z]{1}\s[a-z]{1}\s[a-z]{1}']
        for pattern in nonsense_patterns:
            if re.search(pattern, text):
                score -= 5

        return score

    def _clean_ocr_text(self, text):
        """清理OCR识别结果"""
        if not text:
            return ""

        # 基本清理
        text = text.strip()

        # 移除明显的错误字符
        text = re.sub(r'\bnnn+\b', '', text)  # 移除nnn
        text = re.sub(r'\bmmm+\b', '', text)  # 移除mmm
        text = re.sub(r'\bxxx+\b', '', text)  # 移除xxx

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        # 清理多余的空行
        text = re.sub(r'\n\s*\n', '\n', text)

        # 如果是表格数据，尝试格式化
        if '|' in text:
            text = self._format_table_text(text)

        return text.strip()

    def _format_table_text(self, text):
        """格式化表格文本"""
        lines = text.split('\n')
        formatted_lines = []

        for line in lines:
            if '|' in line:
                # 清理表格行
                parts = line.split('|')
                cleaned_parts = []

                for part in parts:
                    part = part.strip()
                    if part and len(part) > 1:  # 只保留有意义的部分
                        cleaned_parts.append(part)

                if len(cleaned_parts) >= 2:  # 至少有2列才认为是有效表格行
                    formatted_lines.append(' | '.join(cleaned_parts))
            else:
                if line.strip():
                    formatted_lines.append(line.strip())

        return '\n'.join(formatted_lines)

    def get_paragraph_style_info(self, paragraph):
        """获取段落的详细样式信息"""
        style_info = {
            'is_heading': False,
            'heading_level': 0,
            'is_list_item': False,
            'is_ordered_list': False,
            'list_level': 0,
            'is_bold': False,
            'is_italic': False,
            'alignment': 'left',
            'indent_level': 0,
            'section_context': self._get_section_context(paragraph)
        }

        # 检查是否为标题
        if paragraph.style.name.startswith('Heading'):
            style_info['is_heading'] = True
            try:
                style_info['heading_level'] = int(paragraph.style.name.split()[-1])
            except:
                style_info['heading_level'] = 1

        # 检查是否为列表项
        try:
            # 检查编号列表
            numPr_elements = paragraph._p.xpath('.//w:numPr')
            if numPr_elements:
                style_info['is_list_item'] = True

                # 获取列表级别
                ilvl_elements = paragraph._p.xpath('.//w:ilvl/@w:val')
                if ilvl_elements:
                    style_info['list_level'] = int(ilvl_elements[0])

                # 检查是否为有序列表（根据上下文决定）
                numId_elements = paragraph._p.xpath('.//w:numId/@w:val')
                if numId_elements:
                    # 根据段落所在的章节决定是否使用有序列表
                    style_info['is_ordered_list'] = self._should_use_ordered_list(paragraph, style_info)
            else:
                # 如果没有检测到正式的列表标记，尝试通过文本内容检测
                text = paragraph.text.strip()
                if text:
                    # 检测文本开头的列表标记
                    if text.startswith(('•', '-', '*', '●', '◦', '⁃')):
                        style_info['is_list_item'] = True
                        style_info['is_ordered_list'] = False
                    elif text.startswith(tuple(f'{i}.' for i in range(1, 21))):
                        style_info['is_list_item'] = True
                        style_info['is_ordered_list'] = True
        except:
            pass

        # 检查缩进级别（通过段落的缩进设置）
        try:
            if paragraph.paragraph_format.left_indent:
                # 转换为约略的缩进级别
                indent_pt = paragraph.paragraph_format.left_indent.pt
                style_info['indent_level'] = max(0, int(indent_pt / 36))  # 每36pt为一个缩进级别
        except:
            pass

        # 检查文本格式
        for run in paragraph.runs:
            if run.bold:
                style_info['is_bold'] = True
            if run.italic:
                style_info['is_italic'] = True

        return style_info

    def process_paragraph_text(self, paragraph):
        """处理段落文本，保持格式"""
        text_parts = []

        for run in paragraph.runs:
            text = run.text
            if not text:
                continue

            # 应用格式
            if run.bold and run.italic:
                text = f"***{text}***"
            elif run.bold:
                text = f"**{text}**"
            elif run.italic:
                text = f"*{text}*"

            text_parts.append(text)

        full_text = ''.join(text_parts)

        # 处理回显信息的特殊格式
        if self._is_echo_content(full_text):
            return self._format_echo_content(full_text)

        return full_text

    def convert_table_to_markdown(self, table):
        """将表格转换为Markdown格式"""
        if not table.rows:
            return ""

        markdown_lines = []

        for row_idx, row in enumerate(table.rows):
            cells = []
            for cell in row.cells:
                # 处理单元格内容，替换换行符
                cell_text = cell.text.replace('\n', '<br>').replace('|', '\\|')
                cells.append(cell_text.strip())

            # 添加表格行
            markdown_lines.append(f"| {' | '.join(cells)} |")

            # 在第一行后添加分隔符
            if row_idx == 0:
                separator = ['---'] * len(cells)
                markdown_lines.append(f"| {' | '.join(separator)} |")

        return '\n'.join(markdown_lines)

    def find_images_in_paragraph(self, paragraph):
        """查找段落中的图片"""
        images = []

        # 查找图片关系
        try:
            for run in paragraph.runs:
                for drawing in run._element.xpath('.//w:drawing'):
                    for blip in drawing.xpath('.//a:blip'):
                        embed_id = blip.get(qn('r:embed'))
                        if embed_id:
                            try:
                                # 获取图片关系
                                image_part = paragraph.part.related_parts[embed_id]
                                image_filename = os.path.basename(image_part.partname)

                                print(f"调试: 在段落中找到图片: {image_filename}")

                                # 查找对应的新文件名和OCR文本
                                for original, image_info in self.image_map.items():
                                    if original in image_filename or image_filename in original:
                                        print(f"调试: 匹配成功: {original} -> {image_info['filename']}")
                                        images.append(image_info)
                                        break
                                else:
                                    print(f"调试: 未找到匹配的图片: {image_filename}")
                                    print(f"调试: 可用的图片映射: {list(self.image_map.keys())}")
                            except Exception as e:
                                print(f"调试: 处理图片时出错: {e}")
                                continue
        except Exception as e:
            print(f"调试: 查找图片时出错: {e}")
            pass

        if images:
            print(f"调试: 段落中找到 {len(images)} 张图片")

        return images

    def _paragraph_contains_image(self, paragraph):
        """检查段落是否包含图片"""
        try:
            for run in paragraph.runs:
                if run._element.xpath('.//w:drawing'):
                    return True
        except:
            pass
        return False

    def _process_image(self, image_info, markdown_content):
        """处理图片元素"""
        if isinstance(image_info, dict) and image_info.get('ocr_text'):
            # 如果有OCR文本，显示文本内容（保持上下文缩进）
            ocr_text = image_info['ocr_text']

            # 获取当前的缩进级别（根据最近的列表项或段落）
            current_indent = self._get_current_indent_level(markdown_content)
            indent = '   ' * current_indent

            # 直接显示OCR内容，不加标题
            markdown_content.append(f"{indent}```")
            # 处理多行内容，每行都加上缩进
            for line in ocr_text.split('\n'):
                if line.strip():
                    markdown_content.append(f"{indent}{line.strip()}")
                else:
                    markdown_content.append("")
            markdown_content.append(f"{indent}```")
            markdown_content.append("")
        elif isinstance(image_info, dict):
            # 没有OCR文本，使用图片链接
            image_name = image_info['filename']
            image_path = f"images/{image_name}"
            current_indent = self._get_current_indent_level(markdown_content)
            indent = '   ' * current_indent
            markdown_content.append(f"{indent}![图片]({image_path})")
            markdown_content.append("")

    def _get_current_indent_level(self, markdown_content):
        """获取当前的缩进级别（根据最近的内容）"""
        if not markdown_content:
            return 0

        # 查找最近的非空行
        for i in range(len(markdown_content) - 1, -1, -1):
            line = markdown_content[i]
            if line.strip():  # 非空行
                # 计算缩进级别
                leading_spaces = len(line) - len(line.lstrip())
                indent_level = leading_spaces // 3  # 每3个空格为一级缩进

                # 如果是列表项，增加一级缩进
                stripped_line = line.strip()
                if (stripped_line.startswith(('1. ', '2. ', '3. ', '4. ', '5. ', '6. ', '7. ', '8. ', '9. ')) or
                    stripped_line.startswith(tuple(f'{i}. ' for i in range(10, 100))) or
                    stripped_line.startswith('- ')):
                    return indent_level + 1

                return indent_level

        return 0

    def convert_to_markdown(self, output_filename="document.md"):
        """主转换函数"""
        print("开始转换文档...")

        # 提取图片并进行OCR识别
        print("提取图片...")
        self.extract_images()

        markdown_content = []
        markdown_content.append("# 文档转换结果\n")

        # 初始化序号计数器
        self.step_counter = 0
        self.current_section = ""

        # 处理文档元素
        print("处理文档内容...")

        # 获取所有文档元素（段落、表格和图片）
        elements = []
        image_counter = 0

        for element in self.doc.element.body:
            if element.tag.endswith('p'):  # 段落
                # 找到对应的段落对象
                for para in self.doc.paragraphs:
                    if para._element == element:
                        elements.append(('paragraph', para))

                        # 检查段落是否包含图片
                        if self._paragraph_contains_image(para):
                            image_counter += 1
                            # 添加图片元素
                            if image_counter <= len(self.image_map):
                                image_info = list(self.image_map.values())[image_counter - 1]
                                elements.append(('image', image_info))
                        break
            elif element.tag.endswith('tbl'):  # 表格
                # 找到对应的表格对象
                for table in self.doc.tables:
                    if table._tbl == element:
                        elements.append(('table', table))
                        break

        # 处理每个元素
        for elem_type, elem in elements:
            if elem_type == 'paragraph':
                self._process_paragraph(elem, markdown_content)
            elif elem_type == 'table':
                self._process_table(elem, markdown_content)
            elif elem_type == 'image':
                self._process_image(elem, markdown_content)

        # 不显示图片内容，只保留文档格式转换

        # 写入文件
        output_path = os.path.join(self.output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        print(f"转换完成！")
        print(f"Markdown文件: {output_path}")
        print(f"图片目录: {self.image_dir}")

        return output_path

    def _process_paragraph(self, paragraph, markdown_content):
        """处理段落"""
        text = paragraph.text.strip()
        if not text:
            return

        style_info = self.get_paragraph_style_info(paragraph)
        formatted_text = self.process_paragraph_text(paragraph)

        # 更新当前章节
        if style_info['is_heading']:
            self.current_section = formatted_text
            if '处理步骤' in formatted_text:
                self.step_counter = 0  # 重置步骤计数器

        # 图片现在作为独立元素处理，不在段落中处理
        # images = self.find_images_in_paragraph(paragraph)

        # 处理标题
        if style_info['is_heading']:
            level = style_info['heading_level']
            markdown_content.append(f"{'#' * level} {formatted_text}")
            markdown_content.append("")  # 空行

        # 处理小标题（在处理步骤中的无序号小标题）
        elif self._is_subsection_title(paragraph, style_info):
            markdown_content.append("")  # 空行
            markdown_content.append(f"**{formatted_text}**")
            markdown_content.append("")  # 空行

        # 处理列表项
        elif style_info['is_list_item']:
            # 清理文本开头的列表标记
            clean_text = formatted_text
            if clean_text.startswith(('•', '-', '*', '●', '◦', '⁃')):
                clean_text = clean_text[1:].strip()
            elif clean_text.startswith(tuple(f'{i}.' for i in range(1, 21))):
                # 移除数字和点
                clean_text = clean_text[clean_text.find('.')+1:].strip()

            indent = '   ' * style_info['list_level']  # 使用3个空格作为缩进

            if style_info['is_ordered_list']:
                # 有序列表
                if '处理步骤' in self.current_section and style_info['list_level'] == 0:
                    # 在处理步骤章节中的最外层列表使用连续编号
                    self.step_counter += 1
                    markdown_content.append(f"{indent}{self.step_counter}. {clean_text}")
                else:
                    # 其他情况使用默认编号
                    markdown_content.append(f"{indent}1. {clean_text}")
            else:
                # 无序列表 - 统一使用短横线
                markdown_content.append(f"{indent}- {clean_text}")

        # 处理有缩进但不是列表的段落（可能是列表项的补充说明）
        elif style_info['indent_level'] > 0 or self._should_indent(paragraph, markdown_content):
            # 计算缩进级别
            indent_level = max(style_info['indent_level'], self._calculate_context_indent(markdown_content))
            indent = '   ' * indent_level

            # 检查是否是代码块
            if self._is_code_block(formatted_text):
                markdown_content.append(f"{indent}```")
                markdown_content.append(f"{indent}{formatted_text}")
                markdown_content.append(f"{indent}```")
            elif self._is_echo_content(formatted_text):
                # 处理回显信息，每行都加上缩进
                echo_lines = formatted_text.split('\n')
                for line in echo_lines:
                    if line.strip():
                        markdown_content.append(f"{indent}{line.strip()}")
                    else:
                        markdown_content.append("")
            else:
                markdown_content.append(f"{indent}{formatted_text}")

        # 处理普通段落
        else:
            # 检查是否是代码块或特殊格式
            if self._is_code_block(formatted_text):
                markdown_content.append(f"```")
                markdown_content.append(formatted_text)
                markdown_content.append(f"```")
            else:
                markdown_content.append(formatted_text)
            markdown_content.append("")  # 空行

        # 图片现在作为独立元素处理

    def _is_code_block(self, text):
        """检查是否为代码块"""
        # 简单的代码块检测逻辑
        code_indicators = [
            'su - root',
            'TMOUT=0',
            'source set_env',
            'cpssafe',
            'cps template-instance-list',
            'cps host-template-instance-operate',
            'log policy-get',
            'log policy-set',
            'log log-flush',
            'log log-state-get'
        ]

        for indicator in code_indicators:
            if indicator in text:
                return True

        # 检查是否以命令开头
        if text.startswith(('$', '#', '>', 'Input command:')):
            return True

        return False

    def _is_echo_content(self, text):
        """检查是否为回显信息内容"""
        echo_indicators = [
            'please choose environment variable',
            'openstack environment variable',
            'cps environment variable',
            'please choose:[',
            'Input command:',
            '回显如下类似信息',
            '显示如下信息'
        ]

        for indicator in echo_indicators:
            if indicator in text:
                return True

        # 检查是否包含多行选项格式
        if '(1)' in text and '(2)' in text:
            return True

        return False

    def _format_echo_content(self, text):
        """格式化回显信息内容"""
        # 处理选项列表格式
        if 'please choose environment variable' in text:
            lines = []
            parts = text.split('(')

            # 处理第一部分
            if parts[0].strip():
                lines.append(parts[0].strip())

            # 处理选项
            for i, part in enumerate(parts[1:], 1):
                if ')' in part:
                    option_text = part.split(')', 1)
                    if len(option_text) == 2:
                        lines.append(f"({i}) {option_text[1].strip()}")

            # 处理最后的提示
            if 'please choose:' in text:
                choice_part = text[text.find('please choose:'):]
                lines.append(choice_part.strip())

            return '\n'.join(lines)

        # 其他情况直接返回
        return text

    def _get_section_context(self, paragraph):
        """获取段落所在的章节上下文"""
        # 向上查找最近的标题
        doc_elements = list(self.doc.element.body)
        current_element = paragraph._element

        try:
            current_index = doc_elements.index(current_element)

            # 向上查找最近的标题
            for i in range(current_index - 1, -1, -1):
                element = doc_elements[i]
                if element.tag.endswith('p'):
                    for para in self.doc.paragraphs:
                        if para._element == element:
                            if para.style.name.startswith('Heading'):
                                return para.text.strip()
                            break
        except:
            pass

        return ""

    def _should_use_ordered_list(self, paragraph, style_info):
        """根据上下文决定是否使用有序列表"""
        section_context = style_info['section_context']
        list_level = style_info['list_level']

        # 可能原因章节使用无序列表
        if '可能原因' in section_context:
            return False

        # 处理步骤章节：最外层用有序，内层用无序
        if '处理步骤' in section_context:
            return list_level == 0  # 只有最外层（级别0）使用有序列表

        # 其他情况默认使用有序列表
        return True

    def _is_subsection_title(self, paragraph, style_info):
        """检查是否为小标题（在处理步骤中的无序号小标题）"""
        text = paragraph.text.strip()
        section_context = style_info['section_context']

        # 只在处理步骤章节中检查
        if '处理步骤' not in section_context:
            return False

        # 如果已经被识别为列表项，则不是小标题
        if style_info['is_list_item']:
            return False

        # 只检查精确的小标题模式（不包含动作性语言）
        exact_subsection_patterns = [
            'Apacheproxy服务状态异常',
            '日志配置文件中OBS服务IP、端口未正确设置'
        ]

        # 只匹配精确的模式
        for pattern in exact_subsection_patterns:
            if text == pattern or text.strip() == pattern:
                return True

        return False

    def _should_indent(self, paragraph, markdown_content):
        """检查段落是否应该缩进（基于上下文）"""
        if not markdown_content:
            return False

        # 检查最近的几行是否是列表项
        for i in range(min(3, len(markdown_content))):
            recent_line = markdown_content[-(i+1)].strip()
            if recent_line.startswith(('1. ', '- ', '* ')):
                return True

        # 检查段落内容是否像是列表项的补充说明
        text = paragraph.text.strip()
        if text.startswith(('具体请参见', '默认', '系统同时支持', '显示如下', '回显如下')):
            return True

        return False

    def _calculate_context_indent(self, markdown_content):
        """根据上下文计算合适的缩进级别"""
        if not markdown_content:
            return 0

        # 查找最近的列表项
        for i in range(min(5, len(markdown_content))):
            recent_line = markdown_content[-(i+1)].strip()
            if recent_line.startswith('1. ') or recent_line.startswith('- '):
                # 计算该行的缩进级别
                leading_spaces = len(markdown_content[-(i+1)]) - len(markdown_content[-(i+1)].lstrip())
                return (leading_spaces // 3) + 1  # 比列表项多一级缩进

        return 1  # 默认缩进一级

    def _process_table(self, table, markdown_content):
        """处理表格"""
        table_md = self.convert_table_to_markdown(table)
        if table_md:
            markdown_content.append(table_md)
            markdown_content.append("")  # 空行


def main():
    """主函数"""
    # 配置
    input_file = "test.docx"
    output_dir = "markdown_output"

    if not os.path.exists(input_file):
        print(f"错误：找不到文件 {input_file}")
        return

    # 创建转换器
    converter = EnhancedDocxToMarkdown(input_file, output_dir)

    # 执行转换
    try:
        output_file = converter.convert_to_markdown("test_converted.md")
        print(f"\n✅ 转换成功！")
        print(f"📄 输出文件: {output_file}")
        print(f"🖼️  图片目录: {converter.image_dir}")

        # 显示统计信息
        print(f"\n📊 转换统计:")
        print(f"   - 段落数: {len(converter.doc.paragraphs)}")
        print(f"   - 表格数: {len(converter.doc.tables)}")
        print(f"   - 图片数: {len(converter.image_map)}")

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
